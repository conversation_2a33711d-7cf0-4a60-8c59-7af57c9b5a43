import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../services/push_notification_service.dart';

class FCMSettingsScreen extends StatefulWidget {
  const FCMSettingsScreen({super.key});

  @override
  State<FCMSettingsScreen> createState() => _FCMSettingsScreenState();
}

class _FCMSettingsScreenState extends State<FCMSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _serverKeyController = TextEditingController();
  final _serviceAccountController = TextEditingController();
  bool _isLoading = false;
  bool _isObscured = true;
  bool _fcmV1Enabled = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _serverKeyController.dispose();
    _serviceAccountController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final serverKey = prefs.getString('fcm_server_key') ?? '';
      final serviceAccount = prefs.getString('fcm_service_account_key') ?? '';
      final fcmV1Enabled = prefs.getBool('fcm_v1_enabled') ?? false;

      setState(() {
        _serverKeyController.text = serverKey;
        _serviceAccountController.text = serviceAccount;
        _fcmV1Enabled = fcmV1Enabled;
      });
    } catch (e) {
      print('Error loading settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_server_key', _serverKeyController.text.trim());
      await prefs.setString('fcm_service_account_key', _serviceAccountController.text.trim());
      await PushNotificationService.setFCMv1Enabled(_fcmV1Enabled);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('FCM Settings saved successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving FCM settings: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Settings'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Firebase Cloud Messaging Configuration',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              // FCM API Version Toggle
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'FCM API Version',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      SwitchListTile(
                        title: const Text('Use FCM API v1'),
                        subtitle: Text(
                          _fcmV1Enabled
                              ? 'Using Firebase Cloud Messaging API (HTTP v1) - Recommended'
                              : 'Using Legacy FCM API - Will be deprecated on June 20, 2024',
                          style: TextStyle(
                            color: _fcmV1Enabled ? Colors.green : Colors.orange,
                            fontSize: AppConstants.fontSizeSmall,
                          ),
                        ),
                        value: _fcmV1Enabled,
                        onChanged: (value) {
                          setState(() {
                            _fcmV1Enabled = value;
                          });
                        },
                      ),
                      if (!_fcmV1Enabled)
                        Container(
                          padding: const EdgeInsets.all(AppConstants.paddingSmall),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                            border: Border.all(color: Colors.orange),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.warning, color: Colors.orange, size: 16),
                              const SizedBox(width: AppConstants.paddingSmall),
                              Expanded(
                                child: Text(
                                  'Legacy FCM API will be deprecated on June 20, 2024. Please migrate to FCM API v1.',
                                  style: TextStyle(
                                    color: Colors.orange[800],
                                    fontSize: AppConstants.fontSizeSmall,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              // Instructions based on API version
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: AppConstants.infoColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  border: Border.all(color: AppConstants.infoColor.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _fcmV1Enabled
                          ? 'How to get your Service Account Key:'
                          : 'How to get your FCM Server Key:',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: AppConstants.fontSizeMedium,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    if (_fcmV1Enabled) ...[
                      const Text(
                        '1. Go to Firebase Console (console.firebase.google.com)',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                      const Text(
                        '2. Select your project',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                      const Text(
                        '3. Go to Project Settings > Service Accounts',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                      const Text(
                        '4. Click "Generate new private key" and download the JSON file',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                      const Text(
                        '5. Copy the entire JSON content and paste it below',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                    ] else ...[
                      const Text(
                        '1. Go to Firebase Console (console.firebase.google.com)',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                      const Text(
                        '2. Select your project',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                      const Text(
                        '3. Go to Project Settings > Cloud Messaging',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                      const Text(
                        '4. Copy the Server Key from the "Project credentials" section',
                        style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: AppConstants.paddingLarge),

              // Conditional input fields based on API version
              if (_fcmV1Enabled) ...[
                TextFormField(
                  controller: _serviceAccountController,
                  decoration: const InputDecoration(
                    labelText: 'Service Account JSON',
                    hintText: 'Paste your service account JSON here',
                    border: OutlineInputBorder(),
                    helperText: 'Complete JSON content from the downloaded service account file',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter the Service Account JSON';
                    }
                    try {
                      // Basic JSON validation
                      final json = value.trim();
                      if (!json.startsWith('{') || !json.endsWith('}')) {
                        return 'Invalid JSON format';
                      }
                    } catch (e) {
                      return 'Invalid JSON format';
                    }
                    return null;
                  },
                  maxLines: null,
                  minLines: 8,
                  keyboardType: TextInputType.multiline,
                ),
              ] else ...[
                TextFormField(
                  controller: _serverKeyController,
                  obscureText: _isObscured,
                  decoration: InputDecoration(
                    labelText: 'FCM Server Key',
                    hintText: 'Enter your Firebase Cloud Messaging Server Key',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(_isObscured ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _isObscured = !_isObscured;
                        });
                      },
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter the FCM Server Key';
                    }
                    if (!value.trim().startsWith('AAAA')) {
                      return 'FCM Server Key should start with "AAAA"';
                    }
                    return null;
                  },
                  // Remove maxLines and minLines when obscureText is true
                  // Flutter doesn't allow multiline with obscureText
                  maxLines: _isObscured ? 1 : null,
                  minLines: _isObscured ? 1 : 3,
                  keyboardType: _isObscured ? TextInputType.text : TextInputType.multiline,
                ),
              ],
              const SizedBox(height: AppConstants.paddingLarge),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(_fcmV1Enabled ? 'Save FCM v1 Settings' : 'Save FCM Settings'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
