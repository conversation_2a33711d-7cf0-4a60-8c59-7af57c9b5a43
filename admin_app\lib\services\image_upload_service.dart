import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';

class ImageUploadService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final ImagePicker _picker = ImagePicker();

  /// Pick image from gallery
  static Future<XFile?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );
      return image;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }

  /// Check Firebase Storage configuration
  static Future<bool> checkFirebaseStorageConfig() async {
    try {
      // Try to get a reference to test connectivity
      final ref = _storage.ref().child('test');
      await ref.getMetadata();
      return true;
    } catch (e) {
      print('Firebase Storage configuration error: $e');
      return false;
    }
  }

  /// Upload image to Firebase Storage
  static Future<String?> uploadCategoryIcon(XFile imageFile, String categoryId) async {
    try {
      print('Starting category icon upload for categoryId: $categoryId');

      // Check Firebase Storage configuration first
      print('Checking Firebase Storage configuration...');
      final isConfigured = await checkFirebaseStorageConfig();
      if (!isConfigured) {
        throw Exception('Firebase Storage is not properly configured');
      }
      print('Firebase Storage configuration OK');

      // Validate file first
      if (!isValidImageFile(imageFile)) {
        print('Invalid image file: ${imageFile.name}');
        throw Exception('Invalid image file format');
      }

      if (!await isValidFileSize(imageFile)) {
        print('File size too large: ${await getFileSizeInMB(imageFile)}MB');
        throw Exception('File size exceeds 5MB limit');
      }

      // Read image bytes
      print('Reading image bytes...');
      final Uint8List imageBytes = await imageFile.readAsBytes();
      print('Image bytes read successfully: ${imageBytes.length} bytes');

      // Create reference
      final String fileName = 'category_icon_${categoryId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final Reference ref = _storage.ref().child('category_icons').child(fileName);
      print('Created storage reference: ${ref.fullPath}');

      // Upload file with progress tracking
      print('Starting upload...');
      final UploadTask uploadTask = ref.putData(
        imageBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'categoryId': categoryId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      // Monitor upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        double progress = snapshot.bytesTransferred / snapshot.totalBytes;
        print('Upload progress: ${(progress * 100).toStringAsFixed(1)}%');
      });

      // Wait for upload to complete
      print('Waiting for upload to complete...');
      final TaskSnapshot snapshot = await uploadTask;
      print('Upload completed. State: ${snapshot.state}');

      if (snapshot.state != TaskState.success) {
        throw Exception('Upload failed with state: ${snapshot.state}');
      }

      // Get download URL
      print('Getting download URL...');
      final String downloadUrl = await snapshot.ref.getDownloadURL();
      print('Download URL obtained: $downloadUrl');

      return downloadUrl;
    } catch (e) {
      print('Error uploading category icon: $e');
      print('Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  /// Delete image from Firebase Storage
  static Future<bool> deleteCategoryIcon(String imageUrl) async {
    try {
      final Reference ref = _storage.refFromURL(imageUrl);
      await ref.delete();
      return true;
    } catch (e) {
      print('Error deleting category icon: $e');
      return false;
    }
  }

  /// Validate image file
  static bool isValidImageFile(XFile file) {
    // Check file name/path extension
    final String fileName = file.name.toLowerCase();
    final List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    // Check if file name contains a valid extension
    bool hasValidExtension = allowedExtensions.any((ext) => fileName.endsWith('.$ext'));

    // Also check MIME type if available
    if (file.mimeType != null) {
      final List<String> allowedMimeTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
      ];
      bool hasValidMimeType = allowedMimeTypes.contains(file.mimeType!.toLowerCase());
      return hasValidExtension || hasValidMimeType;
    }

    return hasValidExtension;
  }

  /// Get file size in MB
  static Future<double> getFileSizeInMB(XFile file) async {
    final int bytes = await file.length();
    return bytes / (1024 * 1024);
  }

  /// Validate file size (max 5MB)
  static Future<bool> isValidFileSize(XFile file, {double maxSizeMB = 5.0}) async {
    final double sizeMB = await getFileSizeInMB(file);
    return sizeMB <= maxSizeMB;
  }
}
